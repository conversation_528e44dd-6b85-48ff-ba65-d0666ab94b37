<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="#f5f5f5">

    <!-- Logo/Title Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="48dp">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_launcher"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_title"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="16sp"
            android:textColor="#666666" />

    </LinearLayout>

    <!-- Login Form -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="24dp"
        android:elevation="4dp">

        <!-- Username Field -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_username"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/etUsername"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:inputType="text"
            android:maxLines="1"
            android:imeOptions="actionNext"
            android:background="@android:drawable/edit_text"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <!-- Password Field -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_password"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/etPassword"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:inputType="textPassword"
            android:maxLines="1"
            android:imeOptions="actionDone"
            android:background="@android:drawable/edit_text"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <!-- Remember Me Checkbox -->
        <CheckBox
            android:id="@+id/cbRememberMe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_remember_me"
            android:layout_marginBottom="24dp"
            android:textColor="#666666" />

        <!-- Login Button -->
        <Button
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/login_button"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="#2196F3"
            android:textColor="#ffffff"
            android:elevation="2dp" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:visibility="gone" />

        <!-- Error Message -->
        <TextView
            android:id="@+id/tvError"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textColor="#f44336"
            android:textSize="14sp"
            android:gravity="center"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Footer -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="UHF UART Demo v1.0"
        android:textSize="12sp"
        android:textColor="#999999" />

</LinearLayout>
