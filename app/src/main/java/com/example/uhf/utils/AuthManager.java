package com.example.uhf.utils;

import android.content.Context;
import android.text.TextUtils;
import java.util.HashMap;
import java.util.Map;

/**
 * Authentication Manager for handling user credentials and validation
 * Manages user authentication logic and credential validation
 */
public class AuthManager {
    
    private static AuthManager instance;
    private Context context;
    private SessionManager sessionManager;
    
    // Default credentials (in production, these should come from a secure backend)
    private static final Map<String, String> DEFAULT_CREDENTIALS = new HashMap<>();
    
    static {
        // Default admin credentials
        DEFAULT_CREDENTIALS.put("admin", "admin123");
        DEFAULT_CREDENTIALS.put("user", "user123");
        DEFAULT_CREDENTIALS.put("demo", "demo123");
        DEFAULT_CREDENTIALS.put("uhf", "uhf2024");
    }
    
    private AuthManager(Context context) {
        this.context = context.getApplicationContext();
        this.sessionManager = new SessionManager(this.context);
    }
    
    /**
     * Get singleton instance of AuthManager
     * @param context Application context
     * @return AuthManager instance
     */
    public static synchronized AuthManager getInstance(Context context) {
        if (instance == null) {
            instance = new AuthManager(context);
        }
        return instance;
    }
    
    /**
     * Authenticate user with username and password
     * @param username User's username
     * @param password User's password
     * @return AuthResult containing success status and message
     */
    public AuthResult authenticate(String username, String password) {
        // Input validation
        if (TextUtils.isEmpty(username)) {
            return new AuthResult(false, "Username cannot be empty");
        }
        
        if (TextUtils.isEmpty(password)) {
            return new AuthResult(false, "Password cannot be empty");
        }
        
        // Trim whitespace
        username = username.trim();
        password = password.trim();
        
        // Validate credentials
        if (isValidCredentials(username, password)) {
            return new AuthResult(true, "Authentication successful");
        } else {
            return new AuthResult(false, "Invalid username or password");
        }
    }
    
    /**
     * Login user and create session
     * @param username User's username
     * @param password User's password
     * @param rememberMe Whether to remember the user
     * @return AuthResult containing success status and message
     */
    public AuthResult login(String username, String password, boolean rememberMe) {
        AuthResult authResult = authenticate(username, password);
        
        if (authResult.isSuccess()) {
            // Create login session
            sessionManager.createLoginSession(username, rememberMe);
        }
        
        return authResult;
    }
    
    /**
     * Logout current user
     */
    public void logout() {
        sessionManager.logoutUser();
    }
    
    /**
     * Check if user is currently logged in
     * @return true if user is logged in
     */
    public boolean isLoggedIn() {
        return sessionManager.isLoggedIn();
    }
    
    /**
     * Get current logged in username
     * @return username or null if not logged in
     */
    public String getCurrentUsername() {
        return sessionManager.getUsername();
    }
    
    /**
     * Get session manager instance
     * @return SessionManager instance
     */
    public SessionManager getSessionManager() {
        return sessionManager;
    }
    
    /**
     * Validate username and password against stored credentials
     * @param username User's username
     * @param password User's password
     * @return true if credentials are valid
     */
    private boolean isValidCredentials(String username, String password) {
        // Check against default credentials
        String storedPassword = DEFAULT_CREDENTIALS.get(username.toLowerCase());
        return storedPassword != null && storedPassword.equals(password);
    }
    
    /**
     * Add or update user credentials (for demo purposes)
     * In production, this should be handled by a secure backend
     * @param username User's username
     * @param password User's password
     */
    public void addUser(String username, String password) {
        if (!TextUtils.isEmpty(username) && !TextUtils.isEmpty(password)) {
            DEFAULT_CREDENTIALS.put(username.toLowerCase().trim(), password.trim());
        }
    }
    
    /**
     * Remove user credentials
     * @param username User's username to remove
     */
    public void removeUser(String username) {
        if (!TextUtils.isEmpty(username)) {
            DEFAULT_CREDENTIALS.remove(username.toLowerCase().trim());
        }
    }
    
    /**
     * Check if username exists
     * @param username User's username
     * @return true if username exists
     */
    public boolean userExists(String username) {
        if (TextUtils.isEmpty(username)) {
            return false;
        }
        return DEFAULT_CREDENTIALS.containsKey(username.toLowerCase().trim());
    }
    
    /**
     * Get all available usernames (for demo purposes)
     * @return Array of usernames
     */
    public String[] getAvailableUsernames() {
        return DEFAULT_CREDENTIALS.keySet().toArray(new String[0]);
    }
    
    /**
     * Authentication result class
     */
    public static class AuthResult {
        private boolean success;
        private String message;
        
        public AuthResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
