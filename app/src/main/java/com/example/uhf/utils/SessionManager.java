package com.example.uhf.utils;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * Session Manager for handling user authentication state
 * Manages login sessions, user preferences, and authentication tokens
 */
public class SessionManager {
    
    private static final String PREF_NAME = "UHFLoginSession";
    private static final String KEY_IS_LOGGED_IN = "isLoggedIn";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_REMEMBER_ME = "rememberMe";
    private static final String KEY_LOGIN_TIME = "loginTime";
    private static final String KEY_SESSION_TIMEOUT = "sessionTimeout";
    
    // Session timeout in milliseconds (24 hours)
    private static final long SESSION_TIMEOUT = 24 * 60 * 60 * 1000;
    
    private SharedPreferences pref;
    private SharedPreferences.Editor editor;
    private Context context;
    
    public SessionManager(Context context) {
        this.context = context;
        pref = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = pref.edit();
    }
    
    /**
     * Create login session
     * @param username User's username
     * @param rememberMe Whether to remember the user
     */
    public void createLoginSession(String username, boolean rememberMe) {
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putString(KEY_USERNAME, username);
        editor.putBoolean(KEY_REMEMBER_ME, rememberMe);
        editor.putLong(KEY_LOGIN_TIME, System.currentTimeMillis());
        editor.putLong(KEY_SESSION_TIMEOUT, System.currentTimeMillis() + SESSION_TIMEOUT);
        editor.commit();
    }
    
    /**
     * Check if user is logged in
     * @return true if user is logged in and session is valid
     */
    public boolean isLoggedIn() {
        boolean isLoggedIn = pref.getBoolean(KEY_IS_LOGGED_IN, false);
        
        if (isLoggedIn) {
            // Check if session has expired
            long currentTime = System.currentTimeMillis();
            long sessionTimeout = pref.getLong(KEY_SESSION_TIMEOUT, 0);
            
            if (currentTime > sessionTimeout) {
                // Session expired, logout user
                logoutUser();
                return false;
            }
            
            // Update session timeout if remember me is enabled
            if (pref.getBoolean(KEY_REMEMBER_ME, false)) {
                editor.putLong(KEY_SESSION_TIMEOUT, currentTime + SESSION_TIMEOUT);
                editor.commit();
            }
        }
        
        return isLoggedIn;
    }
    
    /**
     * Get logged in username
     * @return username or null if not logged in
     */
    public String getUsername() {
        if (isLoggedIn()) {
            return pref.getString(KEY_USERNAME, null);
        }
        return null;
    }
    
    /**
     * Check if remember me is enabled
     * @return true if remember me is enabled
     */
    public boolean isRememberMeEnabled() {
        return pref.getBoolean(KEY_REMEMBER_ME, false);
    }
    
    /**
     * Get saved username for remember me functionality
     * @return saved username or empty string
     */
    public String getSavedUsername() {
        if (pref.getBoolean(KEY_REMEMBER_ME, false)) {
            return pref.getString(KEY_USERNAME, "");
        }
        return "";
    }
    
    /**
     * Logout user and clear session
     */
    public void logoutUser() {
        // Clear all session data except remember me preferences
        boolean rememberMe = pref.getBoolean(KEY_REMEMBER_ME, false);
        String savedUsername = "";
        
        if (rememberMe) {
            savedUsername = pref.getString(KEY_USERNAME, "");
        }
        
        editor.clear();
        
        // Restore remember me data if enabled
        if (rememberMe) {
            editor.putBoolean(KEY_REMEMBER_ME, true);
            editor.putString(KEY_USERNAME, savedUsername);
        }
        
        editor.commit();
    }
    
    /**
     * Clear all session data including remember me
     */
    public void clearAllData() {
        editor.clear();
        editor.commit();
    }
    
    /**
     * Update session timeout (extend session)
     */
    public void extendSession() {
        if (isLoggedIn()) {
            editor.putLong(KEY_SESSION_TIMEOUT, System.currentTimeMillis() + SESSION_TIMEOUT);
            editor.commit();
        }
    }
    
    /**
     * Get session remaining time in milliseconds
     * @return remaining time or 0 if not logged in
     */
    public long getSessionRemainingTime() {
        if (isLoggedIn()) {
            long currentTime = System.currentTimeMillis();
            long sessionTimeout = pref.getLong(KEY_SESSION_TIMEOUT, 0);
            return Math.max(0, sessionTimeout - currentTime);
        }
        return 0;
    }
}
