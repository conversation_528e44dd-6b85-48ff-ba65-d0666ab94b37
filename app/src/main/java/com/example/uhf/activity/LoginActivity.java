package com.example.uhf.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.uhf.R;
import com.example.uhf.utils.AuthManager;

/**
 * Login Activity for UHF UART Demo Application
 * Handles user authentication before accessing main functionality
 */
public class LoginActivity extends AppCompatActivity {
    
    private static final String TAG = "LoginActivity";
    
    // UI Components
    private EditText etUsername;
    private EditText etPassword;
    private CheckBox cbRememberMe;
    private Button btnLogin;
    private ProgressBar progressBar;
    private TextView tvError;
    
    // Authentication
    private AuthManager authManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        
        // Initialize authentication manager
        authManager = AuthManager.getInstance(this);
        
        // Check if user is already logged in
        if (authManager.isLoggedIn()) {
            navigateToMainActivity();
            return;
        }
        
        // Initialize UI components
        initializeViews();
        setupEventListeners();
        loadSavedCredentials();
    }
    
    /**
     * Initialize UI components
     */
    private void initializeViews() {
        etUsername = findViewById(R.id.etUsername);
        etPassword = findViewById(R.id.etPassword);
        cbRememberMe = findViewById(R.id.cbRememberMe);
        btnLogin = findViewById(R.id.btnLogin);
        progressBar = findViewById(R.id.progressBar);
        tvError = findViewById(R.id.tvError);
    }
    
    /**
     * Setup event listeners for UI components
     */
    private void setupEventListeners() {
        // Login button click listener
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                attemptLogin();
            }
        });
        
        // Password field enter key listener
        etPassword.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    attemptLogin();
                    return true;
                }
                return false;
            }
        });
    }
    
    /**
     * Load saved credentials if remember me was enabled
     */
    private void loadSavedCredentials() {
        if (authManager.getSessionManager().isRememberMeEnabled()) {
            String savedUsername = authManager.getSessionManager().getSavedUsername();
            if (!TextUtils.isEmpty(savedUsername)) {
                etUsername.setText(savedUsername);
                cbRememberMe.setChecked(true);
                etPassword.requestFocus();
            }
        }
    }
    
    /**
     * Attempt to login with provided credentials
     */
    private void attemptLogin() {
        // Hide error message
        hideError();
        
        // Get input values
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        boolean rememberMe = cbRememberMe.isChecked();
        
        // Validate input
        if (TextUtils.isEmpty(username)) {
            showError(getString(R.string.login_error_empty_username));
            etUsername.requestFocus();
            return;
        }
        
        if (TextUtils.isEmpty(password)) {
            showError(getString(R.string.login_error_empty_password));
            etPassword.requestFocus();
            return;
        }
        
        // Show loading state
        showLoading(true);
        
        // Simulate network delay for better UX
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                performLogin(username, password, rememberMe);
            }
        }, 1000);
    }
    
    /**
     * Perform login authentication
     */
    private void performLogin(String username, String password, boolean rememberMe) {
        AuthManager.AuthResult result = authManager.login(username, password, rememberMe);
        
        showLoading(false);
        
        if (result.isSuccess()) {
            // Login successful
            Toast.makeText(this, "Welcome, " + username + "!", Toast.LENGTH_SHORT).show();
            navigateToMainActivity();
        } else {
            // Login failed
            showError(getString(R.string.login_error_invalid_credentials));
            etPassword.setText("");
            etPassword.requestFocus();
        }
    }
    
    /**
     * Navigate to main activity
     */
    private void navigateToMainActivity() {
        Intent intent = new Intent(this, UHFMainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
    
    /**
     * Show loading state
     */
    private void showLoading(boolean show) {
        if (show) {
            btnLogin.setText(R.string.login_logging_in);
            btnLogin.setEnabled(false);
            progressBar.setVisibility(View.VISIBLE);
        } else {
            btnLogin.setText(R.string.login_button);
            btnLogin.setEnabled(true);
            progressBar.setVisibility(View.GONE);
        }
    }
    
    /**
     * Show error message
     */
    private void showError(String message) {
        tvError.setText(message);
        tvError.setVisibility(View.VISIBLE);
    }
    
    /**
     * Hide error message
     */
    private void hideError() {
        tvError.setVisibility(View.GONE);
    }
    
    @Override
    public void onBackPressed() {
        // Disable back button to prevent bypassing login
        // User must login to access the app
        moveTaskToBack(true);
    }
}
